import { type NextRequest, NextResponse } from "next/server"
import { ensureInitialized } from "./lib/startup"

export function middleware(request: NextRequest) {
  // Initialize the application on first request
  try {
    ensureInitialized()
  } catch (error) {
    console.error("Middleware initialization failed:", error)
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
}
