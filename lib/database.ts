import Database from "better-sqlite3"
import path from "path"

export interface User {
  id: number
  name: string
  email: string
  created_at: string
}

export interface Ticket {
  id: number
  title: string
  description: string
  assigned_user_id: number
  total_estimated_hours: number
  split_size_hours: number
  status: "open" | "in_progress" | "completed"
  created_at: string
}

export interface Module {
  id: number
  ticket_id: number
  module_number: number
  estimated_hours: number
  completed: boolean
  completed_at: string | null
  created_at: string
}

// Initialize SQLite database
const dbPath = path.join(process.cwd(), "database.sqlite")
const db = new Database(dbPath)

// Enable foreign keys
db.pragma("foreign_keys = ON")

export function initializeDatabase() {
  // Create tables
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)

  db.exec(`
    CREATE TABLE IF NOT EXISTS tickets (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      description TEXT NOT NULL,
      assigned_user_id INTEGER NOT NULL,
      total_estimated_hours INTEGER NOT NULL,
      split_size_hours INTEGER NOT NULL,
      status TEXT CHECK(status IN ('open', 'in_progress', 'completed')) DEFAULT 'open',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (assigned_user_id) REFERENCES users (id)
    )
  `)

  db.exec(`
    CREATE TABLE IF NOT EXISTS modules (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      ticket_id INTEGER NOT NULL,
      module_number INTEGER NOT NULL,
      estimated_hours INTEGER NOT NULL,
      completed BOOLEAN DEFAULT FALSE,
      completed_at DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (ticket_id) REFERENCES tickets (id) ON DELETE CASCADE
    )
  `)

  // Insert sample users if table is empty
  const userCount = db.prepare("SELECT COUNT(*) as count FROM users").get() as { count: number }
  if (userCount.count === 0) {
    const insertUser = db.prepare("INSERT INTO users (name, email) VALUES (?, ?)")
    insertUser.run("John Doe", "<EMAIL>")
    insertUser.run("Jane Smith", "<EMAIL>")
    insertUser.run("Bob Johnson", "<EMAIL>")

    console.log("Sample users inserted")
  }

  console.log("Database initialized successfully")
}

export const sqliteDb = {
  // Users
  getAllUsers: () => {
    return db.prepare("SELECT * FROM users ORDER BY created_at DESC").all() as User[]
  },

  getUserById: (id: number) => {
    return db.prepare("SELECT * FROM users WHERE id = ?").get(id) as User | undefined
  },

  createUser: (name: string, email: string) => {
    const stmt = db.prepare("INSERT INTO users (name, email) VALUES (?, ?)")
    const result = stmt.run(name, email)
    return db.prepare("SELECT * FROM users WHERE id = ?").get(result.lastInsertRowid) as User
  },

  updateUser: (id: number, name: string, email: string) => {
    const stmt = db.prepare("UPDATE users SET name = ?, email = ? WHERE id = ?")
    stmt.run(name, email, id)
    return db.prepare("SELECT * FROM users WHERE id = ?").get(id) as User | undefined
  },

  deleteUser: (id: number) => {
    const stmt = db.prepare("DELETE FROM users WHERE id = ?")
    return stmt.run(id).changes > 0
  },

  // Tickets
  getAllTickets: () => {
    return db.prepare("SELECT * FROM tickets ORDER BY created_at DESC").all() as Ticket[]
  },

  getTicketById: (id: number) => {
    return db.prepare("SELECT * FROM tickets WHERE id = ?").get(id) as Ticket | undefined
  },

  createTicket: (
    title: string,
    description: string,
    assigned_user_id: number,
    total_estimated_hours: number,
    split_size_hours: number,
  ) => {
    const insertTicket = db.prepare(`
      INSERT INTO tickets (title, description, assigned_user_id, total_estimated_hours, split_size_hours)
      VALUES (?, ?, ?, ?, ?)
    `)

    const result = insertTicket.run(title, description, assigned_user_id, total_estimated_hours, split_size_hours)
    const ticketId = result.lastInsertRowid as number

    // Create modules
    const moduleCount = Math.ceil(total_estimated_hours / split_size_hours)
    const insertModule = db.prepare(`
      INSERT INTO modules (ticket_id, module_number, estimated_hours)
      VALUES (?, ?, ?)
    `)

    for (let i = 1; i <= moduleCount; i++) {
      const estimatedHours =
        i === moduleCount ? total_estimated_hours - split_size_hours * (moduleCount - 1) : split_size_hours

      insertModule.run(ticketId, i, estimatedHours)
    }

    return db.prepare("SELECT * FROM tickets WHERE id = ?").get(ticketId) as Ticket
  },

  updateTicketStatus: (id: number, status: "open" | "in_progress" | "completed") => {
    const stmt = db.prepare("UPDATE tickets SET status = ? WHERE id = ?")
    stmt.run(status, id)
    return db.prepare("SELECT * FROM tickets WHERE id = ?").get(id) as Ticket | undefined
  },

  // Modules
  getModulesByTicketId: (ticketId: number) => {
    return db.prepare("SELECT * FROM modules WHERE ticket_id = ? ORDER BY module_number").all(ticketId) as Module[]
  },

  getModuleById: (id: number) => {
    return db.prepare("SELECT * FROM modules WHERE id = ?").get(id) as Module | undefined
  },

  updateModuleCompletion: (id: number, completed: boolean) => {
    const completedAt = completed ? new Date().toISOString() : null
    const stmt = db.prepare("UPDATE modules SET completed = ?, completed_at = ? WHERE id = ?")
    stmt.run(completed, completedAt, id)

    // Get the module to find its ticket
    const module = db.prepare("SELECT * FROM modules WHERE id = ?").get(id) as Module
    if (!module) return undefined

    // Update ticket status based on module completion
    const allModules = db.prepare("SELECT * FROM modules WHERE ticket_id = ?").all(module.ticket_id) as Module[]
    const completedModules = allModules.filter((m) => m.completed || (m.id === id && completed))

    let newStatus: "open" | "in_progress" | "completed"
    if (completedModules.length === 0) {
      newStatus = "open"
    } else if (completedModules.length === allModules.length) {
      newStatus = "completed"
    } else {
      newStatus = "in_progress"
    }

    db.prepare("UPDATE tickets SET status = ? WHERE id = ?").run(newStatus, module.ticket_id)

    return db.prepare("SELECT * FROM modules WHERE id = ?").get(id) as Module
  },
}

// Export as mockDb for compatibility with existing imports
export const mockDb = sqliteDb

// Initialize database on import
initializeDatabase()

export default sqliteDb
