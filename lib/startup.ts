import { initializeDatabase } from "./database"

let isInitialized = false

export function ensureInitialized() {
  if (!isInitialized) {
    console.log("Initializing Railway Ticketing System...")

    try {
      // Initialize database tables and seed data
      initializeDatabase()
      console.log("✅ Database initialized successfully")

      // Verify email configuration
      if (process.env.SMTP_USER && process.env.SMTP_PASS) {
        console.log("✅ Email configuration found")
      } else {
        console.log("⚠️  Email configuration not found - notifications will be disabled")
      }

      // Log environment info
      console.log(`🚀 Server starting on port ${process.env.PORT || 3000}`)
      console.log(`🌐 App URL: ${process.env.APP_URL || process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}`)

      isInitialized = true
    } catch (error) {
      console.error("❌ Failed to initialize application:", error)
      throw error
    }
  }
}
