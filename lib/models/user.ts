import db from "../database"

export interface User {
  id: number
  name: string
  email: string
  created_at: string
}

export class UserModel {
  static findAll(): User[] {
    const stmt = db.prepare("SELECT * FROM users ORDER BY name ASC")
    return stmt.all() as User[]
  }

  static findById(id: number): User | null {
    const stmt = db.prepare("SELECT * FROM users WHERE id = ?")
    return stmt.get(id) as User | null
  }

  static findByEmail(email: string): User | null {
    const stmt = db.prepare("SELECT * FROM users WHERE email = ?")
    return stmt.get(email) as User | null
  }

  static create(userData: Omit<User, "id" | "created_at">) {
    const stmt = db.prepare("INSERT INTO users (name, email) VALUES (?, ?)")
    const result = stmt.run(userData.name, userData.email)
    return result.lastInsertRowid as number
  }
}
