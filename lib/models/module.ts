import db from "../database"

export interface Module {
  id: number
  ticket_id: number
  module_number: number
  estimated_hours: number
  completed: boolean
  completed_at?: string
  created_at: string
}

export class ModuleModel {
  static create(moduleData: Omit<Module, "id" | "created_at" | "completed" | "completed_at">) {
    const stmt = db.prepare(`
      INSERT INTO modules (ticket_id, module_number, estimated_hours)
      VALUES (?, ?, ?)
    `)

    const result = stmt.run(moduleData.ticket_id, moduleData.module_number, moduleData.estimated_hours)

    return result.lastInsertRowid as number
  }

  static findByTicketId(ticketId: number): Module[] {
    const stmt = db.prepare(`
      SELECT * FROM modules 
      WHERE ticket_id = ? 
      ORDER BY module_number ASC
    `)

    return stmt.all(ticketId) as Module[]
  }

  static updateCompletion(id: number, completed: boolean) {
    const stmt = db.prepare(`
      UPDATE modules 
      SET completed = ?, completed_at = ? 
      WHERE id = ?
    `)

    const completedAt = completed ? new Date().toISOString() : null
    return stmt.run(completed, completedAt, id)
  }

  static createModulesForTicket(ticketId: number, totalHours: number, splitSize: number) {
    const moduleCount = Math.ceil(totalHours / splitSize)
    const modules = []

    for (let i = 1; i <= moduleCount; i++) {
      const isLastModule = i === moduleCount
      const moduleHours = isLastModule ? totalHours - splitSize * (i - 1) : splitSize

      const moduleId = this.create({
        ticket_id: ticketId,
        module_number: i,
        estimated_hours: moduleHours,
      })

      modules.push(moduleId)
    }

    return modules
  }
}
