import db from "../database"

export interface Ticket {
  id: number
  title: string
  description?: string
  assigned_user_id: number
  total_estimated_hours: number
  split_size_hours: number
  status: string
  created_at: string
}

export interface TicketWithUser extends Ticket {
  assigned_user_name: string
  assigned_user_email: string
}

export class TicketModel {
  static create(ticketData: Omit<Ticket, "id" | "created_at" | "status">) {
    const stmt = db.prepare(`
      INSERT INTO tickets (title, description, assigned_user_id, total_estimated_hours, split_size_hours)
      VALUES (?, ?, ?, ?, ?)
    `)

    const result = stmt.run(
      ticketData.title,
      ticketData.description,
      ticketData.assigned_user_id,
      ticketData.total_estimated_hours,
      ticketData.split_size_hours,
    )

    return result.lastInsertRowid as number
  }

  static findAll(): TicketWithUser[] {
    const stmt = db.prepare(`
      SELECT t.*, u.name as assigned_user_name, u.email as assigned_user_email
      FROM tickets t
      LEFT JOIN users u ON t.assigned_user_id = u.id
      ORDER BY t.created_at DESC
    `)

    return stmt.all() as TicketWithUser[]
  }

  static findById(id: number): TicketWithUser | null {
    const stmt = db.prepare(`
      SELECT t.*, u.name as assigned_user_name, u.email as assigned_user_email
      FROM tickets t
      LEFT JOIN users u ON t.assigned_user_id = u.id
      WHERE t.id = ?
    `)

    return stmt.get(id) as TicketWithUser | null
  }

  static updateStatus(id: number, status: string) {
    const stmt = db.prepare("UPDATE tickets SET status = ? WHERE id = ?")
    return stmt.run(status, id)
  }
}
