import nodemailer from "nodemailer"

interface TicketNotificationData {
  ticketId: number
  title: string
  assignedUserName: string
  totalHours: number
  moduleCount: number
}

// Create transporter with Gmail SMTP
const createTransporter = () => {
  return nodemailer.createTransporter({
    service: "gmail",
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  })
}

// Generate HTML email template
const generateTicketNotificationHTML = (data: TicketNotificationData, progressLink: string) => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Ticket Assignment</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 30px;
          border-radius: 8px 8px 0 0;
          text-align: center;
        }
        .content {
          background: #f8f9fa;
          padding: 30px;
          border-radius: 0 0 8px 8px;
        }
        .ticket-info {
          background: white;
          padding: 20px;
          border-radius: 6px;
          margin: 20px 0;
          border-left: 4px solid #667eea;
        }
        .cta-button {
          display: inline-block;
          background: #667eea;
          color: white;
          padding: 12px 24px;
          text-decoration: none;
          border-radius: 6px;
          font-weight: 600;
          margin: 20px 0;
        }
        .footer {
          text-align: center;
          color: #666;
          font-size: 14px;
          margin-top: 30px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🎫 New Ticket Assignment</h1>
        <p>You have been assigned a new ticket</p>
      </div>
      
      <div class="content">
        <p>Hello <strong>${data.assignedUserName}</strong>,</p>
        
        <p>You have been assigned a new ticket that requires your attention. Here are the details:</p>
        
        <div class="ticket-info">
          <h3>${data.title}</h3>
          <p><strong>Ticket ID:</strong> #${data.ticketId}</p>
          <p><strong>Total Estimated Hours:</strong> ${data.totalHours} hours</p>
          <p><strong>Number of Modules:</strong> ${data.moduleCount}</p>
        </div>
        
        <p>This ticket has been automatically split into <strong>${data.moduleCount} modules</strong> to help you track progress more effectively.</p>
        
        <p>Click the button below to view the ticket details and update your progress:</p>
        
        <a href="${progressLink}" class="cta-button">View Ticket & Update Progress</a>
        
        <p>You can mark individual modules as complete as you work through them. The system will automatically track your overall progress.</p>
        
        <div class="footer">
          <p>This is an automated message from the Railway Ticketing System.</p>
          <p>If you have any questions, please contact your project manager.</p>
        </div>
      </div>
    </body>
    </html>
  `
}

// Generate plain text version
const generateTicketNotificationText = (data: TicketNotificationData, progressLink: string) => {
  return `
New Ticket Assignment

Hello ${data.assignedUserName},

You have been assigned a new ticket:

Ticket: ${data.title}
ID: #${data.ticketId}
Total Estimated Hours: ${data.totalHours} hours
Number of Modules: ${data.moduleCount}

This ticket has been automatically split into ${data.moduleCount} modules to help you track progress.

View ticket and update progress: ${progressLink}

You can mark individual modules as complete as you work through them.

---
This is an automated message from the Railway Ticketing System.
  `
}

export async function sendTicketNotification(recipientEmail: string, ticketData: TicketNotificationData) {
  try {
    // Check if email configuration is available
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
      console.warn("Email configuration not found. Skipping email notification.")
      return { success: false, error: "Email configuration missing" }
    }

    const transporter = createTransporter()

    // Generate progress link
    const baseUrl = process.env.APP_URL || process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
    const progressLink = `${baseUrl}/progress/${ticketData.ticketId}`

    // Generate email content
    const htmlContent = generateTicketNotificationHTML(ticketData, progressLink)
    const textContent = generateTicketNotificationText(ticketData, progressLink)

    // Send email
    const mailOptions = {
      from: {
        name: "Railway Ticketing System",
        address: process.env.SMTP_USER!,
      },
      to: recipientEmail,
      subject: `New Ticket Assignment: ${ticketData.title}`,
      text: textContent,
      html: htmlContent,
    }

    const result = await transporter.sendMail(mailOptions)

    console.log("Email sent successfully:", result.messageId)
    return { success: true, messageId: result.messageId }
  } catch (error) {
    console.error("Failed to send email notification:", error)
    return { success: false, error: error instanceof Error ? error.message : "Unknown error" }
  }
}

// Test email configuration
export async function testEmailConfiguration() {
  try {
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
      return { success: false, error: "Email configuration missing" }
    }

    const transporter = createTransporter()
    await transporter.verify()

    return { success: true, message: "Email configuration is valid" }
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : "Unknown error" }
  }
}

// Send progress update reminder
export async function sendProgressReminder(recipientEmail: string, ticketData: TicketNotificationData) {
  try {
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
      console.warn("Email configuration not found. Skipping reminder email.")
      return { success: false, error: "Email configuration missing" }
    }

    const transporter = createTransporter()
    const baseUrl = process.env.APP_URL || process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
    const progressLink = `${baseUrl}/progress/${ticketData.ticketId}`

    const mailOptions = {
      from: {
        name: "Railway Ticketing System",
        address: process.env.SMTP_USER!,
      },
      to: recipientEmail,
      subject: `Progress Update Reminder: ${ticketData.title}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Progress Update Reminder</h2>
          <p>Hello ${ticketData.assignedUserName},</p>
          <p>This is a friendly reminder to update your progress on ticket <strong>#${ticketData.ticketId}: ${ticketData.title}</strong>.</p>
          <p><a href="${progressLink}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">Update Progress</a></p>
          <p>Thank you!</p>
        </div>
      `,
    }

    const result = await transporter.sendMail(mailOptions)
    return { success: true, messageId: result.messageId }
  } catch (error) {
    console.error("Failed to send reminder email:", error)
    return { success: false, error: error instanceof Error ? error.message : "Unknown error" }
  }
}
