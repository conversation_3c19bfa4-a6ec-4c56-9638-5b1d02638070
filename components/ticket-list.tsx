"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Eye, Clock, User, Calendar } from "lucide-react"
import { TicketDetailDialog } from "./ticket-detail-dialog"

interface Ticket {
  id: number
  title: string
  description?: string
  assigned_user_name: string
  assigned_user_email: string
  total_estimated_hours: number
  split_size_hours: number
  status: string
  created_at: string
}

interface TicketListProps {
  refreshTrigger: number
}

export function TicketList({ refreshTrigger }: TicketListProps) {
  const [tickets, setTickets] = useState<Ticket[]>([])
  const [selectedTicket, setSelectedTicket] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchTickets()
  }, [refreshTrigger])

  const fetchTickets = async () => {
    try {
      const response = await fetch("/api/tickets")
      const data = await response.json()
      if (data.success) {
        setTickets(data.data)
      }
    } catch (error) {
      console.error("Failed to fetch tickets:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-gradient-to-r from-[#0f172a] to-[#1e293b] text-white"
      case "in-progress":
        return "bg-gradient-to-r from-[#fbbf24] to-[#f59e0b] text-[#0f172a]"
      case "completed":
        return "bg-gradient-to-r from-emerald-500 to-emerald-600 text-white"
      default:
        return "bg-gradient-to-r from-slate-500 to-slate-600 text-white"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0f172a]"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 text-[#0f172a]">Ticket</div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-[#0f172a] to-[#fbbf24] bg-clip-text text-transparent">
            All Tickets
          </h2>
        </div>
        <div className="text-sm text-slate-600">{tickets.length} total tickets</div>
      </div>

      {tickets.length === 0 ? (
        <Card className="text-center py-12 bg-white/80 backdrop-blur-sm border-0">
          <CardContent>
            <div className="text-slate-500">No tickets found. Create your first ticket to get started.</div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tickets.map((ticket) => (
            <Card
              key={ticket.id}
              className="group hover:shadow-lg transition-all duration-200 border-0 bg-white/80 backdrop-blur-sm"
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg font-semibold text-slate-800 line-clamp-2">{ticket.title}</CardTitle>
                  <Badge className={`ml-2 border-0 shadow-sm ${getStatusColor(ticket.status)}`}>{ticket.status}</Badge>
                </div>
                {ticket.description && (
                  <CardDescription className="text-slate-600 line-clamp-2">{ticket.description}</CardDescription>
                )}
              </CardHeader>

              <CardContent className="space-y-4">
                <div className="flex items-center gap-4 text-sm text-slate-600">
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    <span>{ticket.assigned_user_name}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    <span>{ticket.total_estimated_hours}h</span>
                  </div>
                </div>

                <div className="flex items-center gap-1 text-sm text-slate-600">
                  <Calendar className="w-4 h-4" />
                  <span>Created {formatDate(ticket.created_at)}</span>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedTicket(ticket.id)}
                  className="w-full hover:bg-[#0f172a]/10 hover:border-[#0f172a] hover:text-[#0f172a] transition-colors"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  View Details
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {selectedTicket && (
        <TicketDetailDialog
          ticketId={selectedTicket}
          open={!!selectedTicket}
          onOpenChange={() => setSelectedTicket(null)}
          onProgressUpdate={fetchTickets}
        />
      )}
    </div>
  )
}
