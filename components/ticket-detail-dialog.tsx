"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Clock, User, CheckCircle, Circle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface Module {
  id: number
  ticket_id: number
  module_number: number
  estimated_hours: number
  completed: boolean
  completed_at?: string
  created_at: string
}

interface TicketDetail {
  id: number
  title: string
  description?: string
  assigned_user_name: string
  assigned_user_email: string
  total_estimated_hours: number
  split_size_hours: number
  status: string
  created_at: string
}

interface TicketProgress {
  completed: number
  total: number
  percentage: number
}

interface TicketDetailDialogProps {
  ticketId: number
  open: boolean
  onOpenChange: (open: boolean) => void
  onProgressUpdate: () => void
}

export function TicketDetailDialog({ ticketId, open, onOpenChange, onProgressUpdate }: TicketDetailDialogProps) {
  const [ticket, setTicket] = useState<TicketDetail | null>(null)
  const [modules, setModules] = useState<Module[]>([])
  const [progress, setProgress] = useState<TicketProgress>({ completed: 0, total: 0, percentage: 0 })
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    if (open && ticketId) {
      fetchTicketDetails()
    }
  }, [open, ticketId])

  const fetchTicketDetails = async () => {
    try {
      const response = await fetch(`/api/progress/${ticketId}`)
      const data = await response.json()

      if (data.success) {
        setTicket(data.data.ticket)
        setModules(data.data.modules)
        setProgress(data.data.progress)
      }
    } catch (error) {
      console.error("Failed to fetch ticket details:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleModuleToggle = async (moduleId: number, completed: boolean) => {
    try {
      const response = await fetch(`/api/modules/${moduleId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ completed }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Module Updated",
          description: `Module ${completed ? "completed" : "marked as incomplete"}`,
        })
        fetchTicketDetails()
        onProgressUpdate()
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to update module",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update module",
        variant: "destructive",
      })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "text-[#0f172a] border-[#0f172a]"
      case "in-progress":
        return "text-[#fbbf24] border-[#fbbf24]"
      case "completed":
        return "text-emerald-600 border-emerald-600"
      default:
        return "text-slate-500 border-slate-300"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[700px] bg-card border-border">
          <div className="p-6 text-center text-muted-foreground">Loading ticket details...</div>
        </DialogContent>
      </Dialog>
    )
  }

  if (!ticket) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[700px] bg-card border-border">
          <div className="p-6 text-center text-muted-foreground">Ticket not found</div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] bg-card border-border max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <DialogTitle className="text-xl text-card-foreground pr-4">{ticket.title}</DialogTitle>
              <DialogDescription className="text-muted-foreground mt-2">
                Ticket #{ticket.id} • Created {formatDate(ticket.created_at)}
              </DialogDescription>
            </div>
            <Badge variant="outline" className={getStatusColor(ticket.status)}>
              {ticket.status}
            </Badge>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Ticket Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <User className="w-4 h-4" />
              <span>
                <strong>Assigned to:</strong> {ticket.assigned_user_name}
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="w-4 h-4" />
              <span>
                <strong>Total Hours:</strong> {ticket.total_estimated_hours}h
              </span>
            </div>
          </div>

          {ticket.description && (
            <div>
              <h4 className="font-medium text-card-foreground mb-2">Description</h4>
              <p className="text-muted-foreground text-sm">{ticket.description}</p>
            </div>
          )}

          {/* Progress Overview */}
          <Card className="bg-muted border-border">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-card-foreground">Progress Overview</CardTitle>
              <CardDescription className="text-muted-foreground">
                {progress.completed} of {progress.total} modules completed
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Progress value={progress.percentage} className="h-2" />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>{progress.percentage}% Complete</span>
                  <span>
                    {progress.completed}/{progress.total} modules
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Modules */}
          <div>
            <h4 className="font-medium text-card-foreground mb-4">Modules</h4>
            <div className="space-y-3">
              {modules.map((module) => (
                <Card key={module.id} className="bg-background border-border">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Checkbox
                          checked={module.completed}
                          onCheckedChange={(checked) => handleModuleToggle(module.id, checked as boolean)}
                          className="border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"
                        />
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-foreground">Module {module.module_number}</span>
                            {module.completed ? (
                              <CheckCircle className="w-4 h-4 text-emerald-600" />
                            ) : (
                              <Circle className="w-4 h-4 text-muted-foreground" />
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {module.estimated_hours}h estimated
                            {module.completed_at && (
                              <span className="ml-2">• Completed {formatDate(module.completed_at)}</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
