"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"

interface User {
  id: number
  name: string
  email: string
}

interface CreateTicketDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onTicketCreated: () => void
}

export function CreateTicketDialog({ open, onOpenChange, onTicketCreated }: CreateTicketDialogProps) {
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    assigned_user_id: "",
    total_estimated_hours: "",
    split_size_hours: "",
  })
  const { toast } = useToast()

  useEffect(() => {
    if (open) {
      fetchUsers()
    }
  }, [open])

  const fetchUsers = async () => {
    try {
      const response = await fetch("/api/users")
      const data = await response.json()
      if (data.success) {
        setUsers(data.data)
      }
    } catch (error) {
      console.error("Failed to fetch users:", error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const response = await fetch("/api/tickets", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          assigned_user_id: Number.parseInt(formData.assigned_user_id),
          total_estimated_hours: Number.parseFloat(formData.total_estimated_hours),
          split_size_hours: Number.parseFloat(formData.split_size_hours),
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Ticket Created",
          description: "The ticket has been created and assigned successfully.",
        })
        setFormData({
          title: "",
          description: "",
          assigned_user_id: "",
          total_estimated_hours: "",
          split_size_hours: "",
        })
        onTicketCreated()
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to create ticket",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create ticket",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] bg-white/95 backdrop-blur-sm border-0 shadow-xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold bg-gradient-to-r from-[#0f172a] to-[#fbbf24] bg-clip-text text-transparent">
            Create New Ticket
          </DialogTitle>
          <DialogDescription className="text-slate-600">
            Create a new ticket and assign it to a team member. The ticket will be automatically split into modules.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title" className="text-slate-700 font-medium">
              Title
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="Enter ticket title"
              required
              className="focus:ring-2 focus:ring-[#0f172a] border-slate-200"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-slate-700 font-medium">
              Description
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Enter ticket description"
              rows={3}
              className="focus:ring-2 focus:ring-[#0f172a] border-slate-200"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="assigned_user" className="text-slate-700 font-medium">
              Assign To
            </Label>
            <Select
              value={formData.assigned_user_id}
              onValueChange={(value) => setFormData({ ...formData, assigned_user_id: value })}
              required
            >
              <SelectTrigger className="focus:ring-2 focus:ring-[#0f172a] border-slate-200">
                <SelectValue placeholder="Select team member" />
              </SelectTrigger>
              <SelectContent>
                {users.map((user) => (
                  <SelectItem key={user.id} value={user.id.toString()}>
                    {user.name} ({user.email})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="total_hours" className="text-slate-700 font-medium">
                Total Hours
              </Label>
              <Input
                id="total_hours"
                type="number"
                step="0.5"
                min="0.5"
                value={formData.total_estimated_hours}
                onChange={(e) => setFormData({ ...formData, total_estimated_hours: e.target.value })}
                placeholder="8"
                required
                className="focus:ring-2 focus:ring-[#0f172a] border-slate-200"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="split_size" className="text-slate-700 font-medium">
                Hours per Module
              </Label>
              <Input
                id="split_size"
                type="number"
                step="0.5"
                min="0.5"
                value={formData.split_size_hours}
                onChange={(e) => setFormData({ ...formData, split_size_hours: e.target.value })}
                placeholder="2"
                required
                className="focus:ring-2 focus:ring-[#0f172a] border-slate-200"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-gradient-to-r from-[#0f172a] to-[#1e293b] hover:from-[#1e293b] hover:to-[#334155] text-white shadow-lg"
            >
              {isLoading ? "Creating..." : "Create Ticket"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
