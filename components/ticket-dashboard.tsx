"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Ticket, Users, Clock, CheckCircle, Sparkles } from "lucide-react"
import { CreateTicketDialog } from "./create-ticket-dialog"
import { TicketList } from "./ticket-list"
import { UserManagement } from "./user-management"

interface DashboardStats {
  totalTickets: number
  openTickets: number
  inProgressTickets: number
  completedTickets: number
  totalUsers: number
}

export function TicketDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalTickets: 0,
    openTickets: 0,
    inProgressTickets: 0,
    completedTickets: 0,
    totalUsers: 0,
  })
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  useEffect(() => {
    fetchDashboardStats()
  }, [refreshTrigger])

  const fetchDashboardStats = async () => {
    try {
      const [ticketsResponse, usersResponse] = await Promise.all([fetch("/api/tickets"), fetch("/api/users")])

      const ticketsData = await ticketsResponse.json()
      const usersData = await usersResponse.json()

      if (ticketsData.success && usersData.success) {
        const tickets = ticketsData.data
        setStats({
          totalTickets: tickets.length,
          openTickets: tickets.filter((t: any) => t.status === "open").length,
          inProgressTickets: tickets.filter((t: any) => t.status === "in-progress").length,
          completedTickets: tickets.filter((t: any) => t.status === "completed").length,
          totalUsers: usersData.data.length,
        })
      }
    } catch (error) {
      console.error("Failed to fetch dashboard stats:", error)
    }
  }

  const handleTicketCreated = () => {
    setRefreshTrigger((prev) => prev + 1)
    setIsCreateDialogOpen(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-amber-50 dark:from-[#0f172a] dark:via-slate-800 dark:to-slate-900">
      <div className="container mx-auto p-6 space-y-8">
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-[#0f172a] via-[#1e293b] to-[#fbbf24] p-8 text-white shadow-2xl">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative flex items-center justify-between">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <Sparkles className="w-8 h-8 text-[#fbbf24]" />
                <h1 className="text-4xl font-bold">Railway Ticketing System</h1>
              </div>
              <p className="text-slate-200 text-lg">Manage your team's tickets and track progress with style</p>
            </div>
            <Button
              onClick={() => setIsCreateDialogOpen(true)}
              className="bg-[#fbbf24] hover:bg-[#f59e0b] text-[#0f172a] font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
            >
              <Plus className="w-5 h-5 mr-2" />
              Create Ticket
            </Button>
          </div>
          <div className="absolute -top-4 -right-4 w-24 h-24 bg-[#fbbf24]/20 rounded-full blur-xl"></div>
          <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-[#0f172a]/20 rounded-full blur-xl"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-[#0f172a]/20 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-700 dark:text-slate-300">Total Tickets</CardTitle>
              <div className="p-2 bg-[#0f172a]/10 dark:bg-[#0f172a]/30 rounded-lg">
                <Ticket className="h-4 w-4 text-[#0f172a] dark:text-[#fbbf24]" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-[#0f172a] dark:text-[#fbbf24]">{stats.totalTickets}</div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-[#fbbf24]/20 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-700 dark:text-slate-300">Open</CardTitle>
              <div className="p-2 bg-[#fbbf24]/10 dark:bg-[#fbbf24]/30 rounded-lg">
                <Clock className="h-4 w-4 text-[#fbbf24] dark:text-[#fbbf24]" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-[#fbbf24] dark:text-[#fbbf24]">{stats.openTickets}</div>
              <Badge className="mt-2 bg-[#fbbf24]/10 text-[#fbbf24] border-[#fbbf24]/30 dark:bg-[#fbbf24]/30 dark:text-[#fbbf24]">
                Pending
              </Badge>
            </CardContent>
          </Card>

          <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-[#0f172a]/20 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-700 dark:text-slate-300">In Progress</CardTitle>
              <div className="p-2 bg-[#0f172a]/10 dark:bg-[#0f172a]/30 rounded-lg">
                <Clock className="h-4 w-4 text-[#0f172a] dark:text-[#fbbf24]" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-[#0f172a] dark:text-[#fbbf24]">{stats.inProgressTickets}</div>
              <Badge className="mt-2 bg-[#0f172a]/10 text-[#0f172a] border-[#0f172a]/30 dark:bg-[#0f172a]/30 dark:text-[#fbbf24]">
                Active
              </Badge>
            </CardContent>
          </Card>

          <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-green-200 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-700 dark:text-slate-300">Completed</CardTitle>
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-700 dark:text-green-300">{stats.completedTickets}</div>
              <Badge className="mt-2 bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-300">
                Done
              </Badge>
            </CardContent>
          </Card>

          <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-slate-200 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-700 dark:text-slate-300">Team Members</CardTitle>
              <div className="p-2 bg-slate-100 dark:bg-slate-700 rounded-lg">
                <Users className="h-4 w-4 text-slate-600 dark:text-slate-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-slate-700 dark:text-slate-300">{stats.totalUsers}</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="tickets" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 bg-white/80 backdrop-blur-sm border border-[#0f172a]/20 shadow-lg">
            <TabsTrigger
              value="tickets"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#0f172a] data-[state=active]:to-[#1e293b] data-[state=active]:text-white font-medium"
            >
              <Ticket className="w-4 h-4 mr-2" />
              Tickets
            </TabsTrigger>
            <TabsTrigger
              value="users"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#0f172a] data-[state=active]:to-[#1e293b] data-[state=active]:text-white font-medium"
            >
              <Users className="w-4 h-4 mr-2" />
              Users
            </TabsTrigger>
          </TabsList>

          <TabsContent value="tickets" className="space-y-6">
            <TicketList refreshTrigger={refreshTrigger} />
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <UserManagement />
          </TabsContent>
        </Tabs>

        {/* Create Ticket Dialog */}
        <CreateTicketDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onTicketCreated={handleTicketCreated}
        />
      </div>
    </div>
  )
}
