"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Clock, User, CheckCircle, Circle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import Link from "next/link"

interface Module {
  id: number
  ticket_id: number
  module_number: number
  estimated_hours: number
  completed: boolean
  completed_at?: string
  created_at: string
}

interface TicketDetail {
  id: number
  title: string
  description?: string
  assigned_user_name: string
  assigned_user_email: string
  total_estimated_hours: number
  split_size_hours: number
  status: string
  created_at: string
}

interface TicketProgress {
  completed: number
  total: number
  percentage: number
}

interface ProgressUpdatePageProps {
  ticketId: number
}

export function ProgressUpdatePage({ ticketId }: ProgressUpdatePageProps) {
  const [ticket, setTicket] = useState<TicketDetail | null>(null)
  const [modules, setModules] = useState<Module[]>([])
  const [progress, setProgress] = useState<TicketProgress>({ completed: 0, total: 0, percentage: 0 })
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    fetchTicketDetails()
  }, [ticketId])

  const fetchTicketDetails = async () => {
    try {
      const response = await fetch(`/api/progress/${ticketId}`)
      const data = await response.json()

      if (data.success) {
        setTicket(data.data.ticket)
        setModules(data.data.modules)
        setProgress(data.data.progress)
      } else {
        toast({
          title: "Error",
          description: "Ticket not found",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Failed to fetch ticket details:", error)
      toast({
        title: "Error",
        description: "Failed to load ticket details",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleModuleToggle = async (moduleId: number, completed: boolean) => {
    try {
      const response = await fetch(`/api/modules/${moduleId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ completed }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Module Updated",
          description: `Module ${completed ? "completed" : "marked as incomplete"}`,
        })
        fetchTicketDetails()
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to update module",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update module",
        variant: "destructive",
      })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "text-chart-3 border-chart-3"
      case "in-progress":
        return "text-chart-5 border-chart-5"
      case "completed":
        return "text-chart-4 border-chart-4"
      default:
        return "text-muted-foreground border-border"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="container mx-auto max-w-4xl">
          <div className="text-center text-muted-foreground">Loading ticket details...</div>
        </div>
      </div>
    )
  }

  if (!ticket) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="container mx-auto max-w-4xl">
          <Card className="bg-card border-border">
            <CardContent className="p-8 text-center">
              <div className="text-muted-foreground">Ticket not found</div>
              <Link href="/">
                <Button variant="outline" className="mt-4 bg-transparent">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="container mx-auto max-w-4xl space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-foreground">Update Progress</h1>
            <p className="text-muted-foreground">Track your progress on this ticket</p>
          </div>
        </div>

        {/* Ticket Info Card */}
        <Card className="bg-card border-border">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <CardTitle className="text-xl text-card-foreground pr-4">{ticket.title}</CardTitle>
                <CardDescription className="text-muted-foreground mt-2">
                  Ticket #{ticket.id} • Created {formatDate(ticket.created_at)}
                </CardDescription>
              </div>
              <Badge variant="outline" className={getStatusColor(ticket.status)}>
                {ticket.status}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <User className="w-4 h-4" />
                <span>
                  <strong>Assigned to:</strong> {ticket.assigned_user_name}
                </span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="w-4 h-4" />
                <span>
                  <strong>Total Hours:</strong> {ticket.total_estimated_hours}h
                </span>
              </div>
            </div>

            {ticket.description && (
              <div>
                <h4 className="font-medium text-card-foreground mb-2">Description</h4>
                <p className="text-muted-foreground text-sm">{ticket.description}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Progress Overview */}
        <Card className="bg-accent/10 border-accent/20">
          <CardHeader>
            <CardTitle className="text-lg text-card-foreground">Progress Overview</CardTitle>
            <CardDescription className="text-muted-foreground">
              {progress.completed} of {progress.total} modules completed
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Progress value={progress.percentage} className="h-3" />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>{progress.percentage}% Complete</span>
                <span>
                  {progress.completed}/{progress.total} modules
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Modules */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-lg text-card-foreground">Modules</CardTitle>
            <CardDescription className="text-muted-foreground">Check off modules as you complete them</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {modules.map((module) => (
                <Card key={module.id} className="bg-muted border-border">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Checkbox
                          checked={module.completed}
                          onCheckedChange={(checked) => handleModuleToggle(module.id, checked as boolean)}
                          className="border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"
                        />
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-foreground">Module {module.module_number}</span>
                            {module.completed ? (
                              <CheckCircle className="w-4 h-4 text-chart-4" />
                            ) : (
                              <Circle className="w-4 h-4 text-muted-foreground" />
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {module.estimated_hours}h estimated
                            {module.completed_at && (
                              <span className="ml-2">• Completed {formatDate(module.completed_at)}</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
