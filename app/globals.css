@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color scheme with exact blue (#0f172a) and gold (#fbbf24) */
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #f8fafc;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --primary: #0f172a; /* Exact dark blue */
  --primary-foreground: #ffffff;
  --secondary: #fbbf24; /* Exact gold */
  --secondary-foreground: #0f172a;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #fbbf24; /* Exact gold */
  --accent-foreground: #0f172a;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #f8fafc;
  --ring: rgba(15, 23, 42, 0.3); /* Blue ring with exact color */
  --chart-1: #0f172a; /* Exact blue */
  --chart-2: #fbbf24; /* Exact gold */
  --chart-3: #1e293b; /* Lighter blue variant */
  --chart-4: #fcd34d; /* Lighter gold variant */
  --chart-5: #334155; /* Medium blue variant */
  --radius: 0.75rem;
  --sidebar: #f8fafc;
  --sidebar-foreground: #0f172a;
  --sidebar-primary: #0f172a;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #fbbf24;
  --sidebar-accent-foreground: #0f172a;
  --sidebar-border: #e2e8f0;
  --sidebar-ring: rgba(15, 23, 42, 0.3);
}

.dark {
  /* Dark mode with exact blue and gold theme */
  --background: #0f172a; /* Using exact blue as dark background */
  --foreground: #f1f5f9;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --popover: #1e293b;
  --popover-foreground: #f1f5f9;
  --primary: #fbbf24; /* Gold as primary in dark mode */
  --primary-foreground: #0f172a;
  --secondary: #334155; /* Lighter blue for secondary in dark */
  --secondary-foreground: #f1f5f9;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --accent: #fbbf24; /* Exact gold accent */
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #f1f5f9;
  --border: #334155;
  --input: #1e293b;
  --ring: rgba(251, 191, 36, 0.3); /* Gold ring in dark mode */
  --chart-1: #fbbf24; /* Gold primary in dark */
  --chart-2: #334155; /* Blue secondary in dark */
  --chart-3: #60a5fa;
  --chart-4: #fcd34d;
  --chart-5: #0ea5e9;
  --sidebar: #1e293b;
  --sidebar-foreground: #f1f5f9;
  --sidebar-primary: #fbbf24;
  --sidebar-primary-foreground: #0f172a;
  --sidebar-accent: #334155;
  --sidebar-accent-foreground: #f1f5f9;
  --sidebar-border: #334155;
  --sidebar-ring: rgba(251, 191, 36, 0.3);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Updated gradient backgrounds with exact colors */
  .gradient-blue-gold {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #fbbf24 100%);
  }

  .gradient-gold-blue {
    background: linear-gradient(135deg, #fbbf24 0%, #fcd34d 50%, #0f172a 100%);
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass-effect {
    background: rgba(30, 41, 59, 0.3);
    border: 1px solid rgba(51, 65, 85, 0.3);
  }
}
