import { type NextRequest, NextResponse } from "next/server"
import { mockDb } from "@/lib/database"

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const moduleId = Number.parseInt(id)

    if (isNaN(moduleId)) {
      return NextResponse.json({ success: false, error: "Invalid module ID" }, { status: 400 })
    }

    const body = await request.json()
    const { completed } = body

    if (typeof completed !== "boolean") {
      return NextResponse.json({ success: false, error: "Completed field must be a boolean" }, { status: 400 })
    }

    const module = mockDb.updateModuleCompletion(moduleId, completed)

    if (!module) {
      return NextResponse.json({ success: false, error: "Module not found" }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: module,
      message: `Module ${completed ? "completed" : "marked as incomplete"}`,
    })
  } catch (error) {
    console.error("Error updating module:", error)
    return NextResponse.json({ success: false, error: "Failed to update module" }, { status: 500 })
  }
}
