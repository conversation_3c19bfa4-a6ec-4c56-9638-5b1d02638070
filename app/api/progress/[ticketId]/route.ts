import { type NextRequest, NextResponse } from "next/server"
import { mockDb } from "@/lib/database"

export async function GET(request: NextRequest, { params }: { params: Promise<{ ticketId: string }> }) {
  try {
    const { ticketId } = await params
    const id = Number.parseInt(ticketId)

    if (isNaN(id)) {
      return NextResponse.json({ success: false, error: "Invalid ticket ID" }, { status: 400 })
    }

    const ticket = mockDb.getTicketById(id)
    if (!ticket) {
      return NextResponse.json({ success: false, error: "Ticket not found" }, { status: 404 })
    }

    const modules = mockDb.getModulesByTicketId(id)
    const assignedUser = mockDb.getUserById(ticket.assigned_user_id)
    const completedModules = modules.filter((m) => m.completed).length
    const totalModules = modules.length
    const progressPercentage = totalModules > 0 ? Math.round((completedModules / totalModules) * 100) : 0

    // Update ticket status based on progress (this is handled automatically in mockDb.updateModuleCompletion)
    const updatedTicket = mockDb.getTicketById(id)

    return NextResponse.json({
      success: true,
      data: {
        ticket: { ...updatedTicket, assigned_user: assignedUser },
        modules,
        progress: {
          completed: completedModules,
          total: totalModules,
          percentage: progressPercentage,
        },
      },
    })
  } catch (error) {
    console.error("Error fetching progress:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch progress" }, { status: 500 })
  }
}
