import { NextResponse } from "next/server"
import { mockDb } from "@/lib/database"

export async function GET() {
  try {
    const users = mockDb.getAllUsers()
    return NextResponse.json({ success: true, data: users })
  } catch (error) {
    console.error("Error fetching users:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch users" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { name, email, role = "developer" } = body

    if (!name || !email) {
      return NextResponse.json({ success: false, error: "Name and email are required" }, { status: 400 })
    }

    // Check if email already exists
    const existingUsers = mockDb.getAllUsers()
    if (existingUsers.some((user) => user.email === email)) {
      return NextResponse.json({ success: false, error: "Email already exists" }, { status: 400 })
    }

    const newUser = mockDb.createUser({ name, email, role })
    return NextResponse.json({ success: true, data: newUser }, { status: 201 })
  } catch (error) {
    console.error("Error creating user:", error)
    return NextResponse.json({ success: false, error: "Failed to create user" }, { status: 500 })
  }
}
