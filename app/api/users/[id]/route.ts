import { NextResponse } from "next/server"
import { mockDb } from "@/lib/database"

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const userId = Number.parseInt(params.id)
    const body = await request.json()
    const { name, email, role } = body

    if (!name || !email) {
      return NextResponse.json({ success: false, error: "Name and email are required" }, { status: 400 })
    }

    // Check if email already exists for other users
    const existingUsers = mockDb.getAllUsers()
    const emailExists = existingUsers.some((user) => user.email === email && user.id !== userId)
    if (emailExists) {
      return NextResponse.json({ success: false, error: "Email already exists" }, { status: 400 })
    }

    const updatedUser = mockDb.updateUser(userId, { name, email, role })
    if (!updatedUser) {
      return NextResponse.json({ success: false, error: "User not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: updatedUser })
  } catch (error) {
    console.error("Error updating user:", error)
    return NextResponse.json({ success: false, error: "Failed to update user" }, { status: 500 })
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const userId = Number.parseInt(params.id)

    // Check if user has assigned tickets
    const tickets = mockDb.getAllTickets()
    const hasAssignedTickets = tickets.some((ticket) => ticket.assignedTo === userId)

    if (hasAssignedTickets) {
      return NextResponse.json(
        {
          success: false,
          error: "Cannot delete user with assigned tickets",
        },
        { status: 400 },
      )
    }

    const deleted = mockDb.deleteUser(userId)
    if (!deleted) {
      return NextResponse.json({ success: false, error: "User not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "User deleted successfully" })
  } catch (error) {
    console.error("Error deleting user:", error)
    return NextResponse.json({ success: false, error: "Failed to delete user" }, { status: 500 })
  }
}
