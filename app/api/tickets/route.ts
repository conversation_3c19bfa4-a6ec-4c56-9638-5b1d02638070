import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase, mockDb } from "@/lib/database"
import { sendTicketNotification } from "@/lib/utils/email"

// Initialize database on first API call
initializeDatabase()

export async function GET() {
  try {
    const tickets = mockDb.getAllTickets()
    // Add user info to tickets
    const ticketsWithUsers = tickets.map((ticket) => {
      const user = mockDb.getUserById(ticket.assigned_user_id)
      return {
        ...ticket,
        assigned_user: user,
      }
    })
    return NextResponse.json({ success: true, data: ticketsWithUsers })
  } catch (error) {
    console.error("Error fetching tickets:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch tickets" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, description, assigned_user_id, total_estimated_hours, split_size_hours } = body

    // Validate required fields
    if (!title || !assigned_user_id || !total_estimated_hours || !split_size_hours) {
      return NextResponse.json({ success: false, error: "Missing required fields" }, { status: 400 })
    }

    // Validate user exists
    const assignedUser = mockDb.getUserById(assigned_user_id)
    if (!assignedUser) {
      return NextResponse.json({ success: false, error: "Assigned user not found" }, { status: 404 })
    }

    // Create ticket (this also creates modules automatically)
    const ticket = mockDb.createTicket(title, description, assigned_user_id, total_estimated_hours, split_size_hours)
    const modules = mockDb.getModulesByTicketId(ticket.id)

    // Send email notification
    try {
      await sendTicketNotification(assignedUser.email, {
        ticketId: ticket.id,
        title,
        assignedUserName: assignedUser.name,
        totalHours: total_estimated_hours,
        moduleCount: modules.length,
      })
    } catch (emailError) {
      console.error("Failed to send email notification:", emailError)
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      data: {
        ticket: { ...ticket, assigned_user: assignedUser },
        modules,
        message: "Ticket created successfully and notification sent",
      },
    })
  } catch (error) {
    console.error("Error creating ticket:", error)
    return NextResponse.json({ success: false, error: "Failed to create ticket" }, { status: 500 })
  }
}
