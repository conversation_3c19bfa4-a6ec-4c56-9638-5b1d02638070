import { type NextRequest, NextResponse } from "next/server"
import { mockDb } from "@/lib/database"

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const ticketId = Number.parseInt(id)

    if (isNaN(ticketId)) {
      return NextResponse.json({ success: false, error: "Invalid ticket ID" }, { status: 400 })
    }

    const ticket = mockDb.getTicketById(ticketId)
    if (!ticket) {
      return NextResponse.json({ success: false, error: "Ticket not found" }, { status: 404 })
    }

    const modules = mockDb.getModulesByTicketId(ticketId)
    const assignedUser = mockDb.getUserById(ticket.assigned_user_id)

    return NextResponse.json({
      success: true,
      data: {
        ticket: { ...ticket, assigned_user: assignedUser },
        modules,
      },
    })
  } catch (error) {
    console.error("Error fetching ticket details:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch ticket details" }, { status: 500 })
  }
}
