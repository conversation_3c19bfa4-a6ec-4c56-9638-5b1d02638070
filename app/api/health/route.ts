import { NextResponse } from "next/server"
import { ensureInitialized } from "@/lib/startup"
import { testEmailConfiguration } from "@/lib/utils/email"

export async function GET() {
  try {
    // Ensure system is initialized
    ensureInitialized()

    // Check email configuration
    const emailStatus = await testEmailConfiguration()

    // Basic health check response
    const healthData = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || "development",
      database: "connected",
      email: emailStatus.success ? "configured" : "not configured",
      version: "1.0.0",
    }

    return NextResponse.json(healthData)
  } catch (error) {
    return NextResponse.json(
      {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
