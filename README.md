# Railway Ticketing System

A lightweight online ticketing system built with Next.js, designed for team project management and hosted on Railway.

## Features

- **Ticket Management**: Create, assign, and track tickets with automatic module splitting
- **Progress Tracking**: Visual progress bars and module completion tracking
- **Email Notifications**: Automatic email notifications with progress update links
- **Team Collaboration**: Multi-user support with assignment capabilities
- **Professional UI**: Clean, modern interface optimized for developers and project managers

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: SQLite with better-sqlite3
- **Email**: Nodemailer with G<PERSON> SMTP
- **Hosting**: Railway (Docker deployment)

## Quick Start

### Local Development

1. **Clone and install dependencies**:
   \`\`\`bash
   git clone <your-repo>
   cd railway-ticketing-system
   npm install
   \`\`\`

2. **Configure environment variables**:
   \`\`\`bash
   cp .env.example .env
   \`\`\`
   
   Update `.env` with your configuration:
   \`\`\`env
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-app-password
   APP_URL=http://localhost:3000
   \`\`\`

3. **Start development server**:
   \`\`\`bash
   npm run dev
   \`\`\`

4. **Open your browser**:
   Navigate to `http://localhost:3000`

### Railway Deployment

1. **Connect your repository** to Railway
2. **Set environment variables** in Railway dashboard:
   - `SMTP_USER`: Your Gmail address
   - `SMTP_PASS`: Your Gmail app password  
   - `APP_URL`: Your Railway app URL (e.g., `https://your-app.railway.app`)
3. **Deploy**: Railway will automatically build and deploy using the included Dockerfile

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `SMTP_USER` | Gmail address for sending notifications | Yes* |
| `SMTP_PASS` | Gmail app password | Yes* |
| `APP_URL` | Application URL for email links | Yes |
| `NODE_ENV` | Environment (development/production) | No |

*Email variables are optional - the system will work without them but notifications will be disabled.

## API Endpoints

### Tickets
- `GET /api/tickets` - List all tickets
- `POST /api/tickets` - Create new ticket
- `GET /api/tickets/[id]` - Get ticket details

### Modules  
- `PUT /api/modules/[id]` - Update module completion status

### Users
- `GET /api/users` - List all users

### System
- `GET /api/health` - Health check endpoint
- `GET /api/progress/[ticketId]` - Get ticket progress (used by email links)

## Database Schema

The system automatically creates these tables on first run:

- **users**: User information and contact details
- **tickets**: Ticket details, assignments, and time estimates  
- **modules**: Auto-generated ticket modules for progress tracking

## Email Configuration

### Gmail Setup

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate an App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. **Use the app password** (not your regular password) in `SMTP_PASS`

### Email Features

- **Automatic notifications** when tickets are created
- **Progress update links** for easy module completion
- **Professional HTML templates** with ticket details
- **Fallback text versions** for all email clients

## Usage

### Creating Tickets

1. Click "Create Ticket" on the dashboard
2. Fill in ticket details:
   - **Title**: Brief description of the work
   - **Description**: Detailed requirements (optional)
   - **Assign To**: Team member responsible
   - **Total Hours**: Estimated time for completion
   - **Hours per Module**: How to split the work
3. Submit - the system will:
   - Create the ticket
   - Split into modules automatically
   - Send email notification to assignee

### Tracking Progress

- **Dashboard view**: See all tickets with status indicators
- **Ticket details**: View modules and completion status
- **Email links**: Direct access to update progress
- **Real-time updates**: Progress bars update automatically

## Development

### Project Structure

\`\`\`
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── progress/          # Progress update pages
│   └── page.tsx           # Main dashboard
├── components/            # React components
├── lib/                   # Utilities and models
│   ├── models/           # Database models
│   └── utils/            # Helper functions
├── scripts/              # Database scripts
└── public/               # Static assets
\`\`\`

### Adding Features

1. **Database changes**: Update models in `lib/models/`
2. **API endpoints**: Add routes in `app/api/`
3. **UI components**: Create in `components/`
4. **Email templates**: Modify `lib/utils/email.ts`

## Troubleshooting

### Common Issues

**Database errors**: The SQLite database is created automatically. Ensure write permissions in the project directory.

**Email not sending**: 
- Verify Gmail app password (not regular password)
- Check 2FA is enabled on Gmail account
- Confirm `SMTP_USER` and `SMTP_PASS` are set correctly

**Railway deployment issues**:
- Ensure environment variables are set in Railway dashboard
- Check build logs for specific errors
- Verify Dockerfile is in project root

### Health Check

Visit `/api/health` to check system status:
- Database connectivity
- Email configuration
- Environment details

## License

MIT License - feel free to use this project for your team's needs.
